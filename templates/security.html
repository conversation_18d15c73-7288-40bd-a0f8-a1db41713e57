{% extends "base.html" %}

{% block content %}
<div class="row">
  <div class="col-12">
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="mb-0">
          <i class="bi bi-shield-lock me-2"></i>Security Settings
        </h3>
      </div>
      <div class="card-body">
        <ul class="nav nav-tabs mb-4" id="securityTabs" role="tablist">

          <li class="nav-item" role="presentation">
            <button class="nav-link {% if active_tab == 'sessions' or not active_tab %}active{% endif %}"
                    id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions"
                    type="button" role="tab" aria-controls="sessions"
                    aria-selected="{% if active_tab == 'sessions' or not active_tab %}true{% else %}false{% endif %}">
              <i class="bi bi-pc-display me-1"></i>Active Sessions
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link {% if active_tab == 'logs' %}active{% endif %}" 
                    id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" 
                    type="button" role="tab" aria-controls="logs" 
                    aria-selected="{% if active_tab == 'logs' %}true{% else %}false{% endif %}">
              <i class="bi bi-journal-text me-1"></i>Security Logs
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link {% if active_tab == 'password' %}active{% endif %}" 
                    id="password-tab" data-bs-toggle="tab" data-bs-target="#password" 
                    type="button" role="tab" aria-controls="password" 
                    aria-selected="{% if active_tab == 'password' %}true{% else %}false{% endif %}">
              <i class="bi bi-key me-1"></i>Password
            </button>
          </li>
        </ul>
        
        <div class="tab-content" id="securityTabsContent">

          
          <!-- Active Sessions Tab -->
          <div class="tab-pane fade {% if active_tab == 'sessions' or not active_tab %}show active{% endif %}" id="sessions" role="tabpanel" aria-labelledby="sessions-tab">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                  <h4 class="card-title mb-0">Active Sessions</h4>
                  <form action="{{ url_for('revoke_all_sessions') }}" method="POST">
                    <button type="submit" class="btn btn-danger btn-sm">
                      <i class="bi bi-x-circle me-1"></i>Revoke All Other Sessions
                    </button>
                  </form>
                </div>
                
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Device</th>
                        <th>IP Address</th>
                        <th>Last Active</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for session_data in active_sessions %}
                      <tr class="{% if session_data.session_id == session.get('session_id') %}table-primary{% endif %}">
                        <td>
                          <div class="d-flex align-items-center">
                            <i class="bi bi-laptop me-2"></i>
                            <div>
                              <div>{{ session_data.user_agent or 'Unknown Device' }}</div>
                              <small class="text-muted">
                                {% if session_data.session_id == session.get('session_id') %}
                                Current Session
                                {% endif %}
                              </small>
                            </div>
                          </div>
                        </td>
                        <td>{{ session_data.ip_address or 'Unknown' }}</td>
                        <td>{{ session_data.last_active.strftime('%Y-%m-%d %H:%M:%S') if session_data.last_active else 'Unknown' }}</td>
                        <td>
                          {% if session_data.session_id != session.get('session_id') %}
                          <form action="{{ url_for('revoke_session', session_id=session_data.session_id) }}" method="POST">
                            <button type="submit" class="btn btn-sm btn-outline-danger">
                              <i class="bi bi-x-circle"></i> Revoke
                            </button>
                          </form>
                          {% else %}
                          <span class="badge bg-primary">Current</span>
                          {% endif %}
                        </td>
                      </tr>
                      {% endfor %}
                      
                      {% if not active_sessions %}
                      <tr>
                        <td colspan="4" class="text-center py-4">
                          <i class="bi bi-info-circle me-2"></i>No active sessions found
                        </td>
                      </tr>
                      {% endif %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Security Logs Tab -->
          <div class="tab-pane fade {% if active_tab == 'logs' %}show active{% endif %}" id="logs" role="tabpanel" aria-labelledby="logs-tab">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <h4 class="card-title mb-4">Security Logs</h4>
                
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Action</th>
                        <th>Timestamp</th>
                        <th>IP Address</th>
                        <th>User Agent</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% for log in security_logs %}
                      <tr>
                        <td>
                          {% set icon = {
                            'login_success': 'bi-box-arrow-in-right text-success',
                            'login_failed': 'bi-x-circle text-danger',
                            'account_locked': 'bi-lock-fill text-danger',
                            'password_changed': 'bi-key text-primary',
                            'password_reset_requested': 'bi-envelope text-warning',
                            '2fa_setup_initiated': 'bi-shield-plus text-primary',
                            '2fa_enabled': 'bi-shield-check text-success',
                            '2fa_disabled': 'bi-shield-x text-danger',
                            '2fa_backup_code_used': 'bi-shield-exclamation text-warning',
                            'session_created': 'bi-plus-circle text-success',
                            'session_terminated': 'bi-x-circle text-danger',
                            'account_created': 'bi-person-plus text-success'
                          }.get(log.action, 'bi-info-circle') %}
                          
                          <div class="d-flex align-items-center">
                            <i class="bi {{ icon }} me-2"></i>
                            <div>{{ log.action.replace('_', ' ').title() }}</div>
                          </div>
                        </td>
                        <td>{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') if log.timestamp else 'Unknown' }}</td>
                        <td>{{ log.ip_address or 'Unknown' }}</td>
                        <td class="text-truncate" style="max-width: 200px;">{{ log.user_agent or 'Unknown' }}</td>
                      </tr>
                      {% endfor %}
                      
                      {% if not security_logs %}
                      <tr>
                        <td colspan="4" class="text-center py-4">
                          <i class="bi bi-info-circle me-2"></i>No security logs found
                        </td>
                      </tr>
                      {% endif %}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Password Tab -->
          <div class="tab-pane fade {% if active_tab == 'password' %}show active{% endif %}" id="password" role="tabpanel" aria-labelledby="password-tab">
            <div class="card border-0 shadow-sm">
              <div class="card-body">
                <h4 class="card-title">Change Password</h4>
                <p class="card-text text-muted">
                  It's a good practice to change your password regularly. Your password should be at least 8 characters long
                  and include uppercase letters, lowercase letters, numbers, and special characters.
                </p>
                
                <form action="{{ url_for('change_password') }}" method="POST" class="mt-4">
                  <div class="mb-3">
                    <label for="current_password" class="form-label">Current Password</label>
                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                  </div>
                  
                  <div class="mb-3">
                    <label for="new_password" class="form-label">New Password</label>
                    <input type="password" class="form-control" id="new_password" name="new_password" required
                           minlength="8" pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$">
                    <div class="form-text">
                      Password must be at least 8 characters long and include uppercase, lowercase, number, and special character
                    </div>
                  </div>
                  
                  <div class="mb-4">
                    <label for="confirm_password" class="form-label">Confirm New Password</label>
                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                  </div>
                  
                  <button type="submit" class="btn btn-primary">
                    <i class="bi bi-check-circle me-2"></i>Change Password
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tabs properly
    var tabEl = document.querySelector('a[data-bs-toggle="tab"]')
    var triggerTabList = [].slice.call(document.querySelectorAll('#securityTabs button'))
    
    triggerTabList.forEach(function (triggerEl) {
      var tabTrigger = new bootstrap.Tab(triggerEl)
      
      triggerEl.addEventListener('click', function (event) {
        event.preventDefault()
        tabTrigger.show()
        
        // Update URL hash without page reload
        const tabTarget = triggerEl.getAttribute('data-bs-target')
        if (tabTarget) {
          history.replaceState(null, null, tabTarget)
        }
      })
    })
    
    // Handle URL hash for direct tab access
    if (window.location.hash) {
      const hash = window.location.hash
      const tabEl = document.querySelector(`button[data-bs-target="${hash}"]`)
      if (tabEl) {
        const tab = new bootstrap.Tab(tabEl)
        tab.show()
      }
    }
    
    // Special handling for password tab when accessed directly
    if (window.location.pathname === '/security/password') {
      const passwordTab = document.querySelector('#password-tab')
      if (passwordTab) {
        const tab = new bootstrap.Tab(passwordTab)
        tab.show()
      }
    }
    
    // Handle form validation for password change
    const passwordForm = document.querySelector('form[action="{{ url_for("change_password") }}"]');
    if (passwordForm) {
      const newPasswordInput = document.getElementById('new_password');
      const confirmPasswordInput = document.getElementById('confirm_password');
      
      passwordForm.addEventListener('submit', function(event) {
        // Check if passwords match
        if (newPasswordInput.value !== confirmPasswordInput.value) {
          event.preventDefault();
          alert('New passwords do not match');
          return false;
        }
        
        // Check password strength
        const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        if (!passwordPattern.test(newPasswordInput.value)) {
          event.preventDefault();
          alert('Password must be at least 8 characters long and include uppercase, lowercase, number, and special character');
          return false;
        }
        
        return true;
      });
    }
  });
</script>
{% endblock %}