#!/usr/bin/env python3
"""
Script to clean up invalid user sessions in MongoDB
This fixes the ObjectId error by removing any invalid user references
"""

import os
import sys
from dotenv import load_dotenv
from pymongo import MongoClient
from bson import ObjectId

# Load environment variables
load_dotenv()

def main():
    try:
        # Connect to MongoDB
        client = MongoClient(os.getenv('MONGODB_URI'))
        db = client.money_tracker
        
        print("🔍 Checking for invalid user data...")
        
        # Check users collection for any invalid ObjectIds
        users = db.users.find()
        valid_users = []
        invalid_count = 0
        
        for user in users:
            try:
                # Try to access the _id field
                user_id = str(user['_id'])
                if ObjectId.is_valid(user_id):
                    valid_users.append(user_id)
                else:
                    print(f"❌ Found invalid user ID: {user_id}")
                    invalid_count += 1
            except Exception as e:
                print(f"❌ Error processing user: {e}")
                invalid_count += 1
        
        print(f"✅ Found {len(valid_users)} valid users")
        if invalid_count > 0:
            print(f"❌ Found {invalid_count} invalid user records")
        
        # Check for any sessions or cookies that might reference invalid user IDs
        # This would typically be handled by clearing browser cookies
        
        print("\n🧹 Cleanup recommendations:")
        print("1. Clear browser cookies for your Money Tracker domain")
        print("2. Restart your Docker containers")
        print("3. If using the app, visit /clear-session endpoint")
        
        if invalid_count > 0:
            print(f"\n⚠️  Found {invalid_count} invalid records that may need manual cleanup")
        else:
            print("\n✅ No invalid user records found!")
            
    except Exception as e:
        print(f"❌ Error connecting to database: {e}")
        print("Make sure your MONGODB_URI is correct in .env file")
        sys.exit(1)
    
    finally:
        if 'client' in locals():
            client.close()

if __name__ == "__main__":
    main()
