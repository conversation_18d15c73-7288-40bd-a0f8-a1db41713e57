// MongoDB initialization script for Money Tracker
db = db.getSiblingDB("money_tracker");
db.createCollection("users");
db.createCollection("expenses");
db.createCollection("salaries");
db.createCollection("categories");
db.createCollection("budgets");
db.users.createIndex({ "username": 1 }, { unique: true });
db.users.createIndex({ "email": 1 }, { unique: true });
print("MongoDB initialization completed for Money Tracker");
