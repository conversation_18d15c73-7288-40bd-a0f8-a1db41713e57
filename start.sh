#!/bin/bash

# Money Tracker Docker Startup Script

echo "🚀 Starting Money Tracker Application..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    if [ -f .env.example ]; then
        cp .env.example .env
        echo "📝 Please edit .env file with your configuration before running again."
        exit 1
    else
        echo "❌ .env.example file not found. Please create .env file manually."
        exit 1
    fi
fi

# Stop any existing containers
echo "⏹️  Stopping existing containers..."
docker compose down

# Build and start containers
echo "🔨 Building and starting containers..."
docker compose up --build -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 15

# Check container status
echo "📊 Container status:"
docker compose ps

# Show logs
echo "📋 Recent logs:"
docker compose logs --tail=10

echo ""
echo "✅ Money Tracker is starting up!"
echo ""
echo "🌐 Application will be available at: http://localhost:5000"
echo "🗄️  MongoDB is available at: localhost:27017"
echo ""
echo "📋 To view logs: docker compose logs -f"
echo "⏹️  To stop: docker compose down"
echo "🔄 To restart: docker compose restart"
