# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=production
FLASK_DEBUG=0

# Security - IMPORTANT: Generate a secure random string for production
SECRET_KEY=your-super-secret-key-change-this-in-production

# Database Configuration
# For MongoDB Atlas (recommended for production)
MONGODB_URI=mongodb+srv://username:<EMAIL>/money_tracker?retryWrites=true&w=majority

# For local MongoDB (if using docker-compose with MongoDB)
# MONGODB_URI=mongodb://mongodb:27017/money_tracker

# Database name
DB_NAME=money_tracker

# Email Configuration (for password reset functionality)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>

# Application Settings
# Set to True only if running behind a reverse proxy with HTTPS
SESSION_COOKIE_SECURE=False

# Logging Level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Rate Limiting (optional - defaults are set in app)
# RATELIMIT_STORAGE_URL=redis://redis:6379

# Timezone (optional)
TZ=UTC

# Health Check Settings
HEALTH_CHECK_ENABLED=True

# Gunicorn Settings (used in Dockerfile)
WEB_CONCURRENCY=4
TIMEOUT=120
KEEP_ALIVE=2
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# Port (for docker-compose override if needed)
PORT=5000
