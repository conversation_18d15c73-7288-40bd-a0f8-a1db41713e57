#!/usr/bin/env python3
"""
Migration script to remove 2FA fields from existing user documents in MongoDB.

This script removes the following fields from all user documents:
- two_factor_enabled
- two_factor_secret
- two_factor_backup_codes

It also adds a security log entry for each user indicating that 2FA has been removed.

Usage:
    python remove_2fa_migration.py

Make sure to backup your database before running this migration!
"""

import os
import sys
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv

def load_environment():
    """Load environment variables from .env file"""
    load_dotenv()
    
    mongodb_uri = os.getenv('MONGODB_URI')
    db_name = os.getenv('DB_NAME')
    
    if not mongodb_uri or not db_name:
        print("Error: MONGODB_URI and DB_NAME must be set in environment variables or .env file")
        sys.exit(1)
    
    return mongodb_uri, db_name

def connect_to_database(mongodb_uri, db_name):
    """Connect to MongoDB database"""
    try:
        client = MongoClient(mongodb_uri)
        db = client[db_name]
        
        # Test the connection
        db.command('ping')
        print(f"Successfully connected to MongoDB database: {db_name}")
        
        return db
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        sys.exit(1)

def remove_2fa_fields(db):
    """Remove 2FA fields from all user documents"""
    users_collection = db.users
    
    # Find all users that have any 2FA fields
    users_with_2fa = users_collection.find({
        '$or': [
            {'two_factor_enabled': {'$exists': True}},
            {'two_factor_secret': {'$exists': True}},
            {'two_factor_backup_codes': {'$exists': True}}
        ]
    })
    
    users_with_2fa_list = list(users_with_2fa)
    total_users = len(users_with_2fa_list)
    
    if total_users == 0:
        print("No users found with 2FA fields. Migration not needed.")
        return
    
    print(f"Found {total_users} users with 2FA fields. Starting migration...")
    
    updated_count = 0
    
    for user in users_with_2fa_list:
        user_id = user['_id']
        email = user.get('email', 'unknown')
        
        try:
            # Prepare the update operation
            update_operation = {
                '$unset': {
                    'two_factor_enabled': '',
                    'two_factor_secret': '',
                    'two_factor_backup_codes': ''
                },
                '$push': {
                    'security_logs': {
                        'action': '2fa_removed_migration',
                        'timestamp': datetime.now(),
                        'ip_address': None,
                        'user_agent': 'Migration Script'
                    }
                }
            }
            
            # Update the user document
            result = users_collection.update_one(
                {'_id': user_id},
                update_operation
            )
            
            if result.modified_count > 0:
                updated_count += 1
                print(f"✓ Updated user: {email}")
            else:
                print(f"⚠ No changes made for user: {email}")
                
        except Exception as e:
            print(f"✗ Error updating user {email}: {e}")
    
    print(f"\nMigration completed!")
    print(f"Total users processed: {total_users}")
    print(f"Users successfully updated: {updated_count}")

def main():
    """Main migration function"""
    print("=" * 60)
    print("2FA Removal Migration Script")
    print("=" * 60)
    print()
    
    # Warning message
    print("⚠️  WARNING: This script will permanently remove all 2FA data from user accounts!")
    print("   Make sure you have backed up your database before proceeding.")
    print()
    
    # Confirmation prompt
    confirmation = input("Do you want to continue? (yes/no): ").lower().strip()
    if confirmation not in ['yes', 'y']:
        print("Migration cancelled.")
        sys.exit(0)
    
    print()
    
    # Load environment and connect to database
    mongodb_uri, db_name = load_environment()
    db = connect_to_database(mongodb_uri, db_name)
    
    # Perform the migration
    remove_2fa_fields(db)
    
    print()
    print("Migration script completed successfully!")

if __name__ == "__main__":
    main()
